import torchvision.transforms as transforms
from channel_aug import ChannelT, ChannelAdapGray, ChannelRandomErasing,ChannelExchange,ChannelRandomErasing
#normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])

def test_transforms(H,W,normalize):
    transform_test = transforms.Compose( [
        transforms.ToPILImage(),
        transforms.Resize((H, W)),
        transforms.ToTensor(),
        normalize])
    return transform_test

def train_transforms_color1(H,W,normalize):
    transform_train=transforms.Compose( [
            transforms.ToPILImage(),#转换为PIL图像
            #transforms.ColorJitter(hue=0.5),#颜色抖动
            transforms.RandomGrayscale(p = 0.5),#随机灰度
            transforms.Pad(10),#边缘填充    
            transforms.RandomCrop((H, W)),#随机裁剪
            transforms.RandomHorizontalFlip(),#随机水平翻转
            transforms.ToTensor(),#转换为张量
            normalize,
            ChannelRandomErasing(probability = 0.5),#随机擦除
            ])
    return transform_train

def train_transforms_color2(H,W,normalize):
    transform_train=transforms.Compose( [
            transforms.ToPILImage(),
            transforms.Pad(10),
            transforms.RandomCrop((H, W)),
            transforms.RandomHorizontalFlip(),
            transforms.ToTensor(),
            normalize,
            ChannelRandomErasing(probability = 0.5),#随机擦除
            ChannelExchange(gray=2)#通道交换
            ])
    return transform_train




def train_transforms_thermal1(H,W,normalize):
    transform_train=transforms.Compose( [
            transforms.ToPILImage(),
            transforms.Pad(10),
            transforms.RandomCrop((H, W)),
            transforms.RandomHorizontalFlip(),
            transforms.ToTensor(),
            normalize,
            ChannelRandomErasing(probability = 0.5),
            ChannelAdapGray(probability =0.5)
            ])
    return transform_train


def train_transforms_thermal2(H,W,normalize):
    transform_train=transforms.Compose( [
            transforms.ToPILImage(),
            transforms.ColorJitter(brightness=0.5),
            transforms.Pad(10),
            transforms.RandomCrop((H, W)),
            transforms.RandomHorizontalFlip(),
            transforms.ToTensor(),
            normalize,
            ChannelRandomErasing(probability = 0.5),
            ChannelT(probability =0.5)           
            ])
    return transform_train




       