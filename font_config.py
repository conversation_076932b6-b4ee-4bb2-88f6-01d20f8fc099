#!/usr/bin/env python3
"""
字体配置模块 - 解决matplotlib中文显示问题
"""

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
import platform

def setup_matplotlib_fonts():
    """
    配置matplotlib字体，解决中文显示问题
    """
    # 基础字体配置
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    # 根据操作系统选择合适的字体
    system = platform.system()
    
    if system == "Windows":
        # Windows系统常用字体
        font_candidates = [
            'Microsoft YaHei',
            'SimHei', 
            'SimSun',
            'KaiTi',
            'Arial Unicode MS',
            'DejaVu Sans'
        ]
    elif system == "Darwin":  # macOS
        # macOS系统常用字体
        font_candidates = [
            'Arial Unicode MS',
            'Hiragino Sans GB',
            'PingFang SC',
            'STHeiti',
            'DejaVu Sans'
        ]
    else:  # Linux
        # Linux系统常用字体
        font_candidates = [
            'WenQuanYi Micro Hei',
            'WenQuanYi Zen Hei',
            'Noto Sans CJK SC',
            'Source Han Sans SC',
            'DejaVu Sans',
            'Liberation Sans'
        ]
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 选择第一个可用的字体
    selected_font = None
    for font in font_candidates:
        if font in available_fonts:
            selected_font = font
            break
    
    if selected_font:
        plt.rcParams['font.sans-serif'] = [selected_font] + plt.rcParams['font.sans-serif']
        print(f"✅ 字体配置成功: {selected_font}")
    else:
        # 如果没有找到合适的字体，使用默认配置
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Liberation Sans']
        print("⚠️  未找到中文字体，使用默认英文字体")
    
    return selected_font

def get_english_labels():
    """
    返回英文标签字典，避免中文显示问题
    """
    return {
        # 特征类型
        'bn_features': 'BN Features',
        'graph_features': 'Hypergraph Features',
        'similarity': 'Cosine Similarity',
        
        # 图表标题
        'norm_distribution': 'Feature Norm Distribution',
        'similarity_distribution': 'Cosine Similarity Distribution',
        'feature_comparison': 'Feature Mean Comparison',
        'feature_difference': 'Feature Difference',
        'tsne_visualization': 't-SNE Visualization',
        
        # 轴标签
        'l2_norm': 'L2 Norm',
        'cosine_similarity': 'Cosine Similarity',
        'feature_dimension': 'Feature Dimension',
        'mean_absolute_difference': 'Mean Absolute Difference',
        'frequency': 'Frequency',
        
        # 其他
        'first_n_dims': 'First {} dims',
        'identity_id': 'ID {}',
    }

def create_comparison_plot_with_english_labels(bn_features, graph_features, save_path=None):
    """
    创建特征对比图，使用英文标签
    """
    labels = get_english_labels()
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. 范数分布
    bn_norms = np.linalg.norm(bn_features, axis=1)
    graph_norms = np.linalg.norm(graph_features, axis=1)
    
    axes[0, 0].hist(bn_norms, bins=30, alpha=0.7, label=labels['bn_features'], color='blue')
    axes[0, 0].hist(graph_norms, bins=30, alpha=0.7, label=labels['graph_features'], color='red')
    axes[0, 0].set_title(labels['norm_distribution'])
    axes[0, 0].set_xlabel(labels['l2_norm'])
    axes[0, 0].set_ylabel(labels['frequency'])
    axes[0, 0].legend()
    
    # 2. 相似度分布
    similarities = []
    for i in range(len(bn_features)):
        sim = np.dot(bn_features[i], graph_features[i]) / (
            np.linalg.norm(bn_features[i]) * np.linalg.norm(graph_features[i]) + 1e-8
        )
        similarities.append(sim)
    
    axes[0, 1].hist(similarities, bins=30, alpha=0.7, color='green')
    axes[0, 1].set_title(labels['similarity_distribution'])
    axes[0, 1].set_xlabel(labels['cosine_similarity'])
    axes[0, 1].set_ylabel(labels['frequency'])
    
    # 3. 特征均值对比
    bn_mean = bn_features.mean(axis=0)
    graph_mean = graph_features.mean(axis=0)
    dim_to_show = min(50, len(bn_mean))
    
    axes[1, 0].plot(bn_mean[:dim_to_show], label=labels['bn_features'], color='blue')
    axes[1, 0].plot(graph_mean[:dim_to_show], label=labels['graph_features'], color='red')
    axes[1, 0].set_title(labels['feature_comparison'] + f' ({labels["first_n_dims"].format(dim_to_show)})')
    axes[1, 0].set_xlabel(labels['feature_dimension'])
    axes[1, 0].legend()
    
    # 4. 特征差异
    diff = np.abs(bn_features - graph_features).mean(axis=0)
    axes[1, 1].plot(diff[:dim_to_show], color='purple')
    axes[1, 1].set_title(labels['feature_difference'] + f' ({labels["first_n_dims"].format(dim_to_show)})')
    axes[1, 1].set_xlabel(labels['feature_dimension'])
    axes[1, 1].set_ylabel(labels['mean_absolute_difference'])
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    
    plt.show()
    
    return fig

def test_font_display():
    """
    测试字体显示效果
    """
    setup_matplotlib_fonts()
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(8, 6))
    
    x = range(10)
    y1 = [i**2 for i in x]
    y2 = [i**1.5 for i in x]
    
    ax.plot(x, y1, label='BN Features', marker='o')
    ax.plot(x, y2, label='Hypergraph Features', marker='s')
    
    ax.set_title('Font Display Test')
    ax.set_xlabel('Feature Dimension')
    ax.set_ylabel('Feature Value')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("字体测试完成！如果图表显示正常，说明字体配置成功。")

if __name__ == "__main__":
    test_font_display()
