#!/usr/bin/env python3
"""
诊断跨模态对比学习问题的脚本
"""

import torch
import numpy as np
from model import embed_net
from config_loader import ConfigLoader

def test_cross_modal_data_flow():
    """测试跨模态对比学习的数据流"""
    print("=== 测试跨模态对比学习数据流 ===")
    
    # 模拟训练数据
    batch_size = 8  # 每个模态8个样本
    
    # 模拟输入
    input10 = torch.randn(batch_size, 3, 384, 192)  # 可见光原图
    input11 = torch.randn(batch_size, 3, 384, 192)  # 可见光增强图
    input20 = torch.randn(batch_size, 3, 384, 192)  # 红外原图
    input21 = torch.randn(batch_size, 3, 384, 192)  # 红外增强图
    
    # 模拟身份标签
    label1 = torch.randint(0, 100, (batch_size,))  # 可见光身份
    label2 = label1.clone()  # 红外身份（应该相同）
    
    print(f"输入数据形状:")
    print(f"  可见光原图: {input10.shape}")
    print(f"  可见光增强图: {input11.shape}")
    print(f"  红外原图: {input20.shape}")
    print(f"  红外增强图: {input21.shape}")
    print(f"  可见光标签: {label1.shape}")
    print(f"  红外标签: {label2.shape}")
    
    # 创建模型
    net = embed_net(
        class_num=100,
        enable_cross_modal=True,
        num_strips=4,
        temperature=0.07,
        contrastive_weight=0.1,  # 降低权重
        projection_dim=128,
        num_heads=4,
    )
    net.eval()
    
    # 准备身份标签
    identities = torch.cat((label1, label2), 0)
    print(f"合并后身份标签: {identities.shape}")
    
    # 前向传播
    try:
        with torch.no_grad():
            feat, out0, loss_sep, feat_logit_styles = net(
                input10, input11, input20, input21,
                modal=0, identities=identities
            )
        
        print(f"\n前向传播成功:")
        print(f"  特征输出: {feat.shape}")
        print(f"  分类输出: {out0.shape}")
        print(f"  损失值: {loss_sep.item():.6f}")
        print(f"  风格分类器输出数量: {len(feat_logit_styles)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_contrastive_loss_computation():
    """测试对比损失计算"""
    print("\n=== 测试对比损失计算 ===")
    
    from cross_modal_contrastive import VectorizedCrossModalContrastive
    
    # 创建对比学习模块
    contrastive_module = VectorizedCrossModalContrastive(
        feat_dim=256,
        num_strips=4,
        temperature=0.07,
        projection_dim=128,
        num_heads=4
    )
    
    # 模拟数据
    batch_size = 4
    num_modalities = 4
    num_strips = 4
    feat_dim = 256
    
    # 创建模拟的条带特征
    modal_strips = torch.randn(batch_size, num_modalities, num_strips, feat_dim)
    identities = torch.randint(0, 10, (batch_size,))
    
    print(f"输入数据:")
    print(f"  模态条带: {modal_strips.shape}")
    print(f"  身份标签: {identities.shape}")
    print(f"  身份值: {identities}")
    
    try:
        enhanced_feat, contrastive_loss, attention_weights = contrastive_module(
            modal_strips, identities
        )
        
        print(f"\n对比学习计算成功:")
        print(f"  增强特征: {enhanced_feat.shape}")
        print(f"  对比损失: {contrastive_loss.item():.6f}")
        print(f"  注意力权重: {attention_weights.shape}")
        
        # 检查损失是否合理
        if contrastive_loss.item() > 0 and contrastive_loss.item() < 10:
            print("✅ 对比损失值在合理范围内")
        else:
            print(f"⚠️  对比损失值异常: {contrastive_loss.item()}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 对比学习计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_hyperparameters():
    """分析超参数设置"""
    print("\n=== 超参数分析 ===")
    
    config_loader = ConfigLoader('config.yaml')
    args = config_loader.get_training_args()
    
    print("当前跨模态对比学习参数:")
    print(f"  enable_cross_modal: {getattr(args, 'enable_cross_modal', 'NOT_SET')}")
    print(f"  temperature: {getattr(args, 'temperature', 'NOT_SET')}")
    print(f"  contrastive_weight: {getattr(args, 'contrastive_weight', 'NOT_SET')}")
    print(f"  projection_dim: {getattr(args, 'projection_dim', 'NOT_SET')}")
    print(f"  num_heads: {getattr(args, 'num_heads', 'NOT_SET')}")
    print(f"  num_strips: {getattr(args, 'num_strips', 'NOT_SET')}")
    
    # 参数建议
    print("\n参数建议:")
    
    temperature = getattr(args, 'temperature', 0.07)
    if temperature < 0.05:
        print("  ⚠️  温度参数过低，可能导致梯度消失")
    elif temperature > 0.2:
        print("  ⚠️  温度参数过高，可能导致对比效果不明显")
    else:
        print("  ✅ 温度参数设置合理")
    
    contrastive_weight = getattr(args, 'contrastive_weight', 0.5)
    if contrastive_weight > 1.0:
        print("  ⚠️  对比损失权重过高，可能压制主任务")
        print("     建议: 从0.1开始，逐步调整")
    elif contrastive_weight < 0.05:
        print("  ⚠️  对比损失权重过低，可能没有效果")
    else:
        print("  ✅ 对比损失权重设置合理")
    
    projection_dim = getattr(args, 'projection_dim', 128)
    if projection_dim < 64:
        print("  ⚠️  投影维度过低，可能信息损失")
    elif projection_dim > 512:
        print("  ⚠️  投影维度过高，可能过拟合")
    else:
        print("  ✅ 投影维度设置合理")

def suggest_improvements():
    """提出改进建议"""
    print("\n=== 改进建议 ===")
    
    print("1. 超参数调优:")
    print("   - 降低 contrastive_weight 从 0.5 到 0.1")
    print("   - 尝试不同的 temperature: [0.05, 0.07, 0.1]")
    print("   - 考虑使用学习率调度器")
    
    print("\n2. 数据处理:")
    print("   - 确保可见光和红外图像确实是同一身份")
    print("   - 检查数据增强是否过度")
    print("   - 验证批次组织逻辑")
    
    print("\n3. 损失函数:")
    print("   - 考虑使用困难样本挖掘")
    print("   - 添加边际约束")
    print("   - 尝试不同的相似度度量")
    
    print("\n4. 训练策略:")
    print("   - 先训练基础模型，再加入对比学习")
    print("   - 使用渐进式权重增加")
    print("   - 监控各个损失分量的变化")
    
    print("\n5. 评估方法:")
    print("   - 单独评估跨模态检索性能")
    print("   - 可视化特征空间分布")
    print("   - 分析模态间的对齐程度")

def main():
    """运行完整的诊断"""
    print("🔍 跨模态对比学习诊断")
    print("=" * 50)
    
    # 运行测试
    test1 = test_cross_modal_data_flow()
    test2 = test_contrastive_loss_computation()
    
    # 分析参数
    analyze_hyperparameters()
    
    # 提出建议
    suggest_improvements()
    
    print("\n" + "=" * 50)
    if test1 and test2:
        print("🎉 基础功能测试通过")
        print("💡 重点关注超参数调优和训练策略")
    else:
        print("❌ 发现基础功能问题，需要先修复代码")
    
    print("\n建议的下一步:")
    print("1. 运行修复后的代码")
    print("2. 使用较小的 contrastive_weight (0.1)")
    print("3. 监控训练过程中的损失变化")
    print("4. 进行消融实验验证效果")

if __name__ == "__main__":
    main()
