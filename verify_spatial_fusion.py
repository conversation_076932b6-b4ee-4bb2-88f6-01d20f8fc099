#!/usr/bin/env python3
"""
验证超图卷积是样本内空间融合的假设
"""

import torch
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from scipy.stats import spearmanr

def simulate_spatial_fusion_effect():
    """
    模拟超图卷积的空间融合效果
    """
    print("=== 模拟超图卷积的空间融合效果 ===")
    
    # 模拟参数
    B, C, H, W = 4, 256, 18, 9  # 4个样本
    V = H * W  # 162个空间位置
    
    # 生成模拟的BN后特征
    torch.manual_seed(42)
    bn_features = torch.randn(B, C, H, W)
    
    print(f"输入特征形状: {bn_features.shape}")
    print(f"空间位置数量: {V}")
    
    # 模拟超图卷积的空间融合过程
    def simulate_hypergraph_conv(x):
        """简化的超图卷积模拟"""
        B, C, H, W = x.shape
        
        # 重塑为顶点表示
        x_flat = x.view(B, C, -1).permute(0, 2, 1)  # [B, V, C]
        
        # 模拟空间融合：每个位置与其邻域进行加权平均
        fused_features = x_flat.clone()
        
        for b in range(B):
            for v in range(V):
                # 计算当前位置的2D坐标
                i, j = v // W, v % W
                
                # 定义邻域（3x3窗口）
                neighbors = []
                for di in [-1, 0, 1]:
                    for dj in [-1, 0, 1]:
                        ni, nj = i + di, j + dj
                        if 0 <= ni < H and 0 <= nj < W:
                            nv = ni * W + nj
                            neighbors.append(nv)
                
                # 加权融合邻域特征
                if len(neighbors) > 1:
                    neighbor_features = x_flat[b, neighbors, :]  # [num_neighbors, C]
                    # 使用注意力权重（简化版）
                    weights = torch.softmax(torch.randn(len(neighbors)), dim=0)
                    fused_features[b, v, :] = torch.sum(
                        neighbor_features * weights.unsqueeze(1), dim=0
                    )
        
        # 重塑回原始形状
        return fused_features.permute(0, 2, 1).view(B, C, H, W)
    
    # 应用模拟的超图卷积
    graph_features = simulate_hypergraph_conv(bn_features)
    
    print(f"超图卷积后特征形状: {graph_features.shape}")
    
    return bn_features, graph_features

def analyze_sample_level_relationships(bn_features, graph_features):
    """
    分析样本级别的关系是否保持
    """
    print("\n=== 分析样本级别关系 ===")
    
    B, C, H, W = bn_features.shape
    
    # 全局平均池化得到样本级表示
    bn_global = torch.mean(bn_features.view(B, C, -1), dim=2)  # [B, C]
    graph_global = torch.mean(graph_features.view(B, C, -1), dim=2)  # [B, C]
    
    print(f"样本级表示形状: {bn_global.shape}")
    
    # 计算样本间距离矩阵
    def compute_distance_matrix(features):
        """计算样本间的余弦距离矩阵"""
        features_norm = torch.nn.functional.normalize(features, p=2, dim=1)
        similarity_matrix = torch.mm(features_norm, features_norm.t())
        distance_matrix = 1 - similarity_matrix
        return distance_matrix.numpy()
    
    bn_distances = compute_distance_matrix(bn_global)
    graph_distances = compute_distance_matrix(graph_global)
    
    print(f"BN特征距离矩阵:\n{bn_distances}")
    print(f"超图特征距离矩阵:\n{graph_distances}")
    
    # 计算距离矩阵的相关性
    # 只取上三角部分（避免对角线和重复）
    triu_indices = np.triu_indices(B, k=1)
    bn_dist_flat = bn_distances[triu_indices]
    graph_dist_flat = graph_distances[triu_indices]
    
    correlation, p_value = spearmanr(bn_dist_flat, graph_dist_flat)
    
    print(f"\n样本间距离相关性:")
    print(f"Spearman相关系数: {correlation:.4f}")
    print(f"p值: {p_value:.4f}")
    
    if correlation > 0.8:
        print("✅ 样本间相对关系高度保持")
        print("   这解释了为什么跨模态对比学习效果相同")
    elif correlation > 0.6:
        print("📊 样本间相对关系部分保持")
    else:
        print("❌ 样本间相对关系显著改变")
    
    return correlation

def analyze_spatial_changes(bn_features, graph_features):
    """
    分析空间维度的变化
    """
    print("\n=== 分析空间维度变化 ===")
    
    B, C, H, W = bn_features.shape
    
    # 计算每个空间位置的特征变化
    bn_flat = bn_features.view(B, C, -1)  # [B, C, V]
    graph_flat = graph_features.view(B, C, -1)  # [B, C, V]
    
    # 计算每个位置的特征相似度
    spatial_similarities = []
    for b in range(B):
        for v in range(H * W):
            bn_vec = bn_flat[b, :, v]
            graph_vec = graph_flat[b, :, v]
            
            # 余弦相似度
            similarity = torch.nn.functional.cosine_similarity(
                bn_vec.unsqueeze(0), graph_vec.unsqueeze(0)
            ).item()
            spatial_similarities.append(similarity)
    
    spatial_similarities = np.array(spatial_similarities)
    
    print(f"空间位置特征相似度统计:")
    print(f"  均值: {spatial_similarities.mean():.4f}")
    print(f"  标准差: {spatial_similarities.std():.4f}")
    print(f"  最小值: {spatial_similarities.min():.4f}")
    print(f"  最大值: {spatial_similarities.max():.4f}")
    
    # 分析空间平滑效果
    def compute_spatial_variance(features):
        """计算空间方差（衡量空间平滑程度）"""
        B, C, H, W = features.shape
        variances = []
        
        for b in range(B):
            for c in range(min(10, C)):  # 只分析前10个通道
                channel_map = features[b, c, :, :]
                # 计算相邻像素的差异
                diff_h = torch.diff(channel_map, dim=0)
                diff_w = torch.diff(channel_map, dim=1)
                variance = torch.mean(diff_h**2) + torch.mean(diff_w**2)
                variances.append(variance.item())
        
        return np.mean(variances)
    
    bn_variance = compute_spatial_variance(bn_features)
    graph_variance = compute_spatial_variance(graph_features)
    
    print(f"\n空间平滑效果:")
    print(f"  BN特征空间方差: {bn_variance:.6f}")
    print(f"  超图特征空间方差: {graph_variance:.6f}")
    print(f"  方差比例: {graph_variance/bn_variance:.4f}")
    
    if graph_variance < bn_variance:
        print("✅ 超图卷积起到了空间平滑作用")
    else:
        print("❌ 超图卷积增加了空间变化")
    
    return spatial_similarities.mean(), bn_variance, graph_variance

def create_visualization(bn_features, graph_features, save_dir='spatial_analysis'):
    """
    创建可视化图表
    """
    import os
    os.makedirs(save_dir, exist_ok=True)
    
    B, C, H, W = bn_features.shape
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 1. 显示第一个样本的第一个通道
    sample_idx, channel_idx = 0, 0
    
    bn_map = bn_features[sample_idx, channel_idx, :, :].numpy()
    graph_map = graph_features[sample_idx, channel_idx, :, :].numpy()
    
    im1 = axes[0, 0].imshow(bn_map, cmap='viridis')
    axes[0, 0].set_title('BN Features (Sample 0, Channel 0)')
    plt.colorbar(im1, ax=axes[0, 0])
    
    im2 = axes[0, 1].imshow(graph_map, cmap='viridis')
    axes[0, 1].set_title('Hypergraph Features (Sample 0, Channel 0)')
    plt.colorbar(im2, ax=axes[0, 1])
    
    # 2. 显示差异图
    diff_map = np.abs(bn_map - graph_map)
    im3 = axes[0, 2].imshow(diff_map, cmap='hot')
    axes[0, 2].set_title('Absolute Difference')
    plt.colorbar(im3, ax=axes[0, 2])
    
    # 3. 特征强度分布
    bn_norms = torch.norm(bn_features.view(B, C, -1), dim=1).flatten().numpy()
    graph_norms = torch.norm(graph_features.view(B, C, -1), dim=1).flatten().numpy()
    
    axes[1, 0].hist(bn_norms, bins=20, alpha=0.7, label='BN Features', color='blue')
    axes[1, 0].hist(graph_norms, bins=20, alpha=0.7, label='Graph Features', color='red')
    axes[1, 0].set_title('Feature Norm Distribution')
    axes[1, 0].legend()
    
    # 4. 样本间距离对比
    bn_global = torch.mean(bn_features.view(B, C, -1), dim=2)
    graph_global = torch.mean(graph_features.view(B, C, -1), dim=2)
    
    bn_distances = torch.cdist(bn_global, bn_global).numpy()
    graph_distances = torch.cdist(graph_global, graph_global).numpy()
    
    axes[1, 1].scatter(bn_distances.flatten(), graph_distances.flatten(), alpha=0.6)
    axes[1, 1].set_xlabel('BN Feature Distances')
    axes[1, 1].set_ylabel('Graph Feature Distances')
    axes[1, 1].set_title('Sample Distance Correlation')
    
    # 5. 空间相似度分布
    spatial_similarities = []
    for b in range(B):
        for v in range(H * W):
            bn_vec = bn_features.view(B, C, -1)[b, :, v]
            graph_vec = graph_features.view(B, C, -1)[b, :, v]
            similarity = torch.nn.functional.cosine_similarity(
                bn_vec.unsqueeze(0), graph_vec.unsqueeze(0)
            ).item()
            spatial_similarities.append(similarity)
    
    axes[1, 2].hist(spatial_similarities, bins=20, alpha=0.7, color='green')
    axes[1, 2].set_title('Spatial Position Similarity')
    axes[1, 2].set_xlabel('Cosine Similarity')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/spatial_fusion_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"可视化结果已保存到 {save_dir}/spatial_fusion_analysis.png")

def main():
    """
    运行完整的空间融合分析
    """
    print("🔍 验证超图卷积的样本内空间融合假设")
    print("=" * 60)
    
    # 模拟超图卷积效果
    bn_features, graph_features = simulate_spatial_fusion_effect()
    
    # 分析样本级别关系
    sample_correlation = analyze_sample_level_relationships(bn_features, graph_features)
    
    # 分析空间变化
    spatial_sim, bn_var, graph_var = analyze_spatial_changes(bn_features, graph_features)
    
    # 创建可视化
    create_visualization(bn_features, graph_features)
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 分析总结")
    print("=" * 60)
    
    print(f"1. 样本间关系保持程度: {sample_correlation:.4f}")
    print(f"2. 空间位置相似度均值: {spatial_sim:.4f}")
    print(f"3. 空间平滑效果: {graph_var/bn_var:.4f} (< 1表示平滑)")
    
    if sample_correlation > 0.8 and spatial_sim > 0.5:
        print("\n✅ 验证结论:")
        print("   - 超图卷积确实是样本内的空间融合")
        print("   - 样本间的相对关系得到保持")
        print("   - 这解释了跨模态对比学习效果相同的原因")
        print("   - 空间融合提升了特征质量，但不改变样本间排序")
    else:
        print("\n❓ 需要进一步分析:")
        print("   - 可能存在其他影响因素")

if __name__ == "__main__":
    main()
