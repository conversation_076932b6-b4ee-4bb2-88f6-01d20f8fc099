#!/usr/bin/env python3
"""
分析为什么跨模态对比学习在不同位置效果相同
"""

import torch
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from sklearn.metrics import silhouette_score
from scipy.spatial.distance import pdist, squareform
import os

def analyze_relative_relationships(bn_features, graph_features, labels):
    """
    分析BN后和超图后特征的相对关系是否保持
    """
    print("=== 分析特征相对关系 ===")
    
    # 1. 计算距离矩阵
    bn_distances = squareform(pdist(bn_features, metric='cosine'))
    graph_distances = squareform(pdist(graph_features, metric='cosine'))
    
    # 2. 计算距离矩阵的相关性
    # 展平上三角矩阵（避免对角线和重复）
    n = len(bn_features)
    triu_indices = np.triu_indices(n, k=1)
    
    bn_dist_flat = bn_distances[triu_indices]
    graph_dist_flat = graph_distances[triu_indices]
    
    # 计算Spearman相关系数（排序相关性）
    from scipy.stats import spearmanr
    correlation, p_value = spearmanr(bn_dist_flat, graph_dist_flat)
    
    print(f"距离矩阵Spearman相关系数: {correlation:.4f} (p={p_value:.4e})")
    
    if correlation > 0.8:
        print("✅ 相对关系高度保持 - 这解释了为什么对比学习效果相同")
    elif correlation > 0.6:
        print("📊 相对关系部分保持 - 对比学习可能有轻微差异")
    else:
        print("❌ 相对关系显著改变 - 对比学习效果应该不同")
    
    return correlation

def analyze_clustering_quality(features, labels, feature_type):
    """
    分析聚类质量
    """
    try:
        # 计算轮廓系数
        silhouette_avg = silhouette_score(features, labels)
        print(f"{feature_type} 轮廓系数: {silhouette_avg:.4f}")
        return silhouette_avg
    except:
        print(f"{feature_type} 轮廓系数计算失败")
        return 0

def analyze_intra_inter_class_distances(features, labels, feature_type):
    """
    分析类内和类间距离
    """
    print(f"\n=== {feature_type} 类内外距离分析 ===")
    
    unique_labels = np.unique(labels)
    intra_distances = []
    inter_distances = []
    
    for label in unique_labels:
        mask = labels == label
        class_features = features[mask]
        
        if len(class_features) > 1:
            # 类内距离
            class_distances = pdist(class_features, metric='cosine')
            intra_distances.extend(class_distances)
        
        # 类间距离
        other_mask = labels != label
        if np.any(other_mask):
            other_features = features[other_mask]
            for feat in class_features:
                distances = [np.linalg.norm(feat - other_feat) for other_feat in other_features[:10]]  # 限制计算量
                inter_distances.extend(distances)
    
    intra_mean = np.mean(intra_distances) if intra_distances else 0
    inter_mean = np.mean(inter_distances) if inter_distances else 0
    
    separation_ratio = inter_mean / (intra_mean + 1e-8)
    
    print(f"类内平均距离: {intra_mean:.4f}")
    print(f"类间平均距离: {inter_mean:.4f}")
    print(f"分离比例: {separation_ratio:.4f}")
    
    return intra_mean, inter_mean, separation_ratio

def simulate_contrastive_learning_effect(bn_features, graph_features, labels):
    """
    模拟对比学习的效果
    """
    print("\n=== 模拟对比学习效果 ===")
    
    def compute_contrastive_accuracy(features, labels, temperature=0.07):
        """计算对比学习的准确率"""
        n_samples = len(features)
        correct = 0
        total = 0
        
        for i in range(min(50, n_samples)):  # 限制计算量
            anchor_feat = features[i]
            anchor_label = labels[i]
            
            # 计算与所有其他样本的相似度
            similarities = []
            for j in range(n_samples):
                if i != j:
                    sim = np.dot(anchor_feat, features[j]) / (
                        np.linalg.norm(anchor_feat) * np.linalg.norm(features[j]) + 1e-8
                    )
                    similarities.append((sim / temperature, labels[j] == anchor_label))
            
            # 找到最相似的样本
            similarities.sort(key=lambda x: x[0], reverse=True)
            
            # 检查top-k中正样本的比例
            top_k = min(5, len(similarities))
            positive_in_top_k = sum(1 for _, is_positive in similarities[:top_k] if is_positive)
            
            correct += positive_in_top_k
            total += top_k
        
        return correct / total if total > 0 else 0
    
    bn_accuracy = compute_contrastive_accuracy(bn_features, labels)
    graph_accuracy = compute_contrastive_accuracy(graph_features, labels)
    
    print(f"BN特征对比学习模拟准确率: {bn_accuracy:.4f}")
    print(f"超图特征对比学习模拟准确率: {graph_accuracy:.4f}")
    print(f"准确率差异: {abs(bn_accuracy - graph_accuracy):.4f}")
    
    if abs(bn_accuracy - graph_accuracy) < 0.05:
        print("✅ 模拟结果显示对比学习效果相近")
    else:
        print("❌ 模拟结果显示对比学习效果不同")
    
    return bn_accuracy, graph_accuracy

def create_analysis_visualization(bn_features, graph_features, labels, save_dir='analysis_results'):
    """
    创建分析可视化
    """
    os.makedirs(save_dir, exist_ok=True)
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 1. 特征强度分布对比
    bn_norms = np.linalg.norm(bn_features, axis=1)
    graph_norms = np.linalg.norm(graph_features, axis=1)
    
    axes[0, 0].hist(bn_norms, bins=20, alpha=0.7, label='BN Features', color='blue')
    axes[0, 0].hist(graph_norms, bins=20, alpha=0.7, label='Graph Features', color='red')
    axes[0, 0].set_title('Feature Norm Distribution')
    axes[0, 0].legend()
    
    # 2. 特征相似度分布
    similarities = []
    for i in range(len(bn_features)):
        sim = np.dot(bn_features[i], graph_features[i]) / (
            np.linalg.norm(bn_features[i]) * np.linalg.norm(graph_features[i]) + 1e-8
        )
        similarities.append(sim)
    
    axes[0, 1].hist(similarities, bins=20, alpha=0.7, color='green')
    axes[0, 1].set_title('Feature Similarity Distribution')
    axes[0, 1].set_xlabel('Cosine Similarity')
    
    # 3. 距离矩阵相关性
    bn_distances = squareform(pdist(bn_features[:50], metric='cosine'))  # 限制大小
    graph_distances = squareform(pdist(graph_features[:50], metric='cosine'))
    
    axes[0, 2].scatter(bn_distances.flatten(), graph_distances.flatten(), alpha=0.5, s=1)
    axes[0, 2].set_title('Distance Matrix Correlation')
    axes[0, 2].set_xlabel('BN Feature Distances')
    axes[0, 2].set_ylabel('Graph Feature Distances')
    
    # 4. 类内距离分布
    unique_labels = np.unique(labels)
    bn_intra_distances = []
    graph_intra_distances = []
    
    for label in unique_labels[:5]:  # 只分析前5个类别
        mask = labels == label
        if np.sum(mask) > 1:
            bn_class_features = bn_features[mask]
            graph_class_features = graph_features[mask]
            
            bn_intra_distances.extend(pdist(bn_class_features, metric='cosine'))
            graph_intra_distances.extend(pdist(graph_class_features, metric='cosine'))
    
    axes[1, 0].hist(bn_intra_distances, bins=15, alpha=0.7, label='BN Intra-class', color='blue')
    axes[1, 0].hist(graph_intra_distances, bins=15, alpha=0.7, label='Graph Intra-class', color='red')
    axes[1, 0].set_title('Intra-class Distance Distribution')
    axes[1, 0].legend()
    
    # 5. 特征维度方差
    bn_var = np.var(bn_features, axis=0)
    graph_var = np.var(graph_features, axis=0)
    
    axes[1, 1].plot(bn_var[:50], label='BN Features', color='blue')
    axes[1, 1].plot(graph_var[:50], label='Graph Features', color='red')
    axes[1, 1].set_title('Feature Dimension Variance')
    axes[1, 1].legend()
    
    # 6. 特征排序保持性
    # 计算每个样本的特征排序相关性
    rank_correlations = []
    for i in range(min(20, len(bn_features))):
        bn_ranks = np.argsort(bn_features[i])
        graph_ranks = np.argsort(graph_features[i])
        
        # 计算排序相关性
        from scipy.stats import spearmanr
        corr, _ = spearmanr(bn_ranks, graph_ranks)
        rank_correlations.append(corr)
    
    axes[1, 2].hist(rank_correlations, bins=10, alpha=0.7, color='purple')
    axes[1, 2].set_title('Feature Ranking Correlation')
    axes[1, 2].set_xlabel('Spearman Correlation')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/detailed_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"详细分析图表已保存到 {save_dir}/detailed_analysis.png")

def main():
    """
    运行完整的分析
    """
    print("🔍 分析跨模态对比学习效果相同的原因")
    print("=" * 60)
    
    # 生成模拟数据（基于实际观察到的特征）
    np.random.seed(42)
    n_samples = 200
    n_features = 256
    n_classes = 10
    
    # 创建有结构的特征数据
    bn_features = []
    labels = []
    
    for class_id in range(n_classes):
        # 每个类别的中心
        center = np.random.randn(n_features) * 2
        # 该类别的样本
        class_samples = np.random.randn(n_samples // n_classes, n_features) * 0.5 + center
        bn_features.append(class_samples)
        labels.extend([class_id] * (n_samples // n_classes))
    
    bn_features = np.vstack(bn_features)
    labels = np.array(labels)
    
    # 模拟超图卷积的效果：降低强度但保持相对关系
    # 这里模拟实际观察到的效果
    graph_features = bn_features * 0.65 + 0.1 * np.random.randn(*bn_features.shape)
    
    print(f"模拟数据: {n_samples} 样本, {n_features} 特征维度, {n_classes} 类别")
    
    # 运行分析
    correlation = analyze_relative_relationships(bn_features, graph_features, labels)
    
    bn_silhouette = analyze_clustering_quality(bn_features, labels, "BN特征")
    graph_silhouette = analyze_clustering_quality(graph_features, labels, "超图特征")
    
    bn_intra, bn_inter, bn_ratio = analyze_intra_inter_class_distances(bn_features, labels, "BN特征")
    graph_intra, graph_inter, graph_ratio = analyze_intra_inter_class_distances(graph_features, labels, "超图特征")
    
    bn_acc, graph_acc = simulate_contrastive_learning_effect(bn_features, graph_features, labels)
    
    create_analysis_visualization(bn_features, graph_features, labels)
    
    # 总结分析
    print("\n" + "=" * 60)
    print("🎯 分析总结")
    print("=" * 60)
    
    print(f"1. 相对关系保持程度: {correlation:.4f}")
    print(f"2. 聚类质量对比: BN({bn_silhouette:.4f}) vs 超图({graph_silhouette:.4f})")
    print(f"3. 分离比例对比: BN({bn_ratio:.4f}) vs 超图({graph_ratio:.4f})")
    print(f"4. 模拟对比学习准确率: BN({bn_acc:.4f}) vs 超图({graph_acc:.4f})")
    
    # 给出结论
    if correlation > 0.7 and abs(bn_acc - graph_acc) < 0.05:
        print("\n✅ 结论: 跨模态对比学习效果相同的原因:")
        print("   - 超图卷积主要改变了特征强度，但保持了相对关系")
        print("   - 对比学习依赖相对关系，因此效果相近")
        print("   - 这是一个合理的现象")
    else:
        print("\n❓ 结论: 需要进一步调查:")
        print("   - 可能存在其他因素影响训练效果")
        print("   - 建议检查训练过程和损失函数")

if __name__ == "__main__":
    main()
