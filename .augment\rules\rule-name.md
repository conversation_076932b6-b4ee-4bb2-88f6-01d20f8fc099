---
type: "always_apply"
description: "Example description"
---
- Do not automatically create example files or markdown documentation unless explicitly requested
- Focus on direct code solutions rather than generating additional files
- When providing code changes, only modify the requested files
- Avoid creating sample configs, README files, or example scripts unless specifically asked
- Keep responses focused on the immediate coding task
- Prefer concise code explanations over verbose documentation
- Use specific variable names from my existing codebase
- Follow my existing code formatting style
- Don't suggest refactoring unless asked
- Focus on Python/PyTorch best practices for deep learning projects
- Do not automatically create command-line execution or testing scripts
- Instead of creating test files, provide direct command-line instructions for users to run manually
- Focus on code solutions and let users handle execution themselves
- When showing how to test changes, provide the actual commands to run rather than creating wrapper scripts