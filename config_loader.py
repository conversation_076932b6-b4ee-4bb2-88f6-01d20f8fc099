#!/usr/bin/env python3
"""
配置文件加载器
用于从YAML文件加载HSFLNet的训练配置参数
"""

import yaml
import argparse
import os
from typing import Dict, Any


class ConfigLoader:
    """配置文件加载器类"""
    
    def __init__(self, config_path: str = 'config.yaml'):
        """
        初始化配置加载器
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        return config
    
    def get_training_args(self) -> argparse.Namespace:
        """
        将配置文件转换为argparse.Namespace对象
        Returns:
            包含所有训练参数的Namespace对象
        """
        args = argparse.Namespace()

        # 核心参数 (最重要)
        core_config = self.config.get('core', {})
        for key, value in core_config.items():
            # 处理batch-size这种带连字符的参数名
            if key == 'batch_size':
                setattr(args, 'batch_size', value)
                setattr(args, 'batch-size', value)  # 兼容性
            else:
                setattr(args, key, value)

        # 超图卷积参数
        hypergraph_config = self.config.get('hypergraph', {})
        for key, value in hypergraph_config.items():
            setattr(args, key, value)

        # 跨模态对比学习参数
        cross_modal_config = self.config.get('cross_modal', {})
        for key, value in cross_modal_config.items():
            # 添加前缀避免命名冲突
            if key == 'enable':
                setattr(args, 'enable_cross_modal', value)
            else:
                setattr(args, key, value)

        # 模型参数
        model_config = self.config.get('model', {})
        for key, value in model_config.items():
            setattr(args, key, value)

        # 训练控制参数
        training_config = self.config.get('training', {})
        for key, value in training_config.items():
            setattr(args, key, value)

        # 数据处理参数
        data_config = self.config.get('data', {})
        for key, value in data_config.items():
            # 处理特殊的参数名
            if key == 'test_batch':
                setattr(args, 'test_batch', value)
                setattr(args, 'test-batch', value)  # 兼容性
            else:
                setattr(args, key, value)

        # 路径配置
        paths_config = self.config.get('paths', {})
        for key, value in paths_config.items():
            setattr(args, key, value)

        # 设置一些默认值以确保兼容性
        default_values = {
            'arch': 'resnet50',
            'backbone': 'AGW',
            'workers': 4,
            'img_w': 192,
            'img_h': 384,
            'margin': 0.7,
            'optim': 'SGD',
            'lr_scheduler': 'step',
            'loss_tri': 'DMC',
            'mode': 'all',
            'resume': '',
            'test_only': False,
        }

        for key, default_value in default_values.items():
            if not hasattr(args, key):
                setattr(args, key, default_value)

        return args
    
    def get_beta_params_for_dataset(self, dataset_name: str) -> Dict[str, Dict[str, float]]:
        """
        获取指定数据集的Beta分布参数
        Args:
            dataset_name: 数据集名称 (sysu, llcm, regdb)
        Returns:
            Beta分布参数字典
        """
        beta_config = self.config.get('beta_distribution', {})
        dataset_params = beta_config.get(dataset_name.lower(), {})
        
        if not dataset_params:
            print(f"警告: 未找到数据集 {dataset_name} 的Beta分布参数，使用SYSU默认参数")
            dataset_params = beta_config.get('sysu', {})
        
        # 转换为模型期望的格式
        formatted_params = {}
        for modality, params in dataset_params.items():
            formatted_params[modality] = {
                'alpha_base': params['alpha'],
                'beta_base': params['beta']
            }
        
        return formatted_params
    
    def apply_experiment_config(self, experiment_name: str):
        """
        应用实验配置模板
        Args:
            experiment_name: 实验配置名称 (baseline, beta_only, diagonal_only, full_model)
        """
        experiments = self.config.get('experiments', {})
        if experiment_name not in experiments:
            print(f"警告: 未找到实验配置 {experiment_name}")
            return
        
        experiment_config = experiments[experiment_name]
        
        # 递归更新配置
        def update_nested_dict(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict:
                    update_nested_dict(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        update_nested_dict(self.config, experiment_config)
        print(f"已应用实验配置: {experiment_name}")
    
    def print_config_summary(self, dataset_name: str = None):
        """
        打印配置摘要
        Args:
            dataset_name: 数据集名称，如果提供则只显示该数据集的Beta参数
        """
        core_config = self.config.get('core', {})
        model_config = self.config.get('model', {})
        hypergraph_config = self.config.get('hypergraph', {})

        print(f"==> HSFLNet配置: {core_config.get('dataset', 'N/A').upper()} | "
              f"{model_config.get('arch', 'N/A')} | "
              f"batch={core_config.get('batch_size', 'N/A')} | "
              f"lr={core_config.get('lr', 'N/A')} | "
              f"GPU={core_config.get('gpu', 'N/A')} | "
              f"seed={core_config.get('seed', 'N/A')}")

        print(f"==> 超图配置: Beta分布={hypergraph_config.get('use_beta_distribution', False)} | "
              f"对角局部性={hypergraph_config.get('use_diagonal', False)} | "
              f"权重={hypergraph_config.get('graphw', 'N/A')} | "
              f"超边={hypergraph_config.get('edge', 'N/A')}")

        # 跨模态对比学习配置
        cross_modal_config = self.config.get('cross_modal', {})
        if cross_modal_config.get('enable', False):
            print(f"==> 跨模态对比学习: 启用 | "
                  f"条带数={cross_modal_config.get('num_strips', 'N/A')} | "
                  f"温度={cross_modal_config.get('temperature', 'N/A')} | "
                  f"权重={cross_modal_config.get('contrastive_weight', 'N/A')}")

        # Beta分布参数 - 简化输出
        if dataset_name and hypergraph_config.get('use_beta_distribution', False):
            beta_params = self.get_beta_params_for_dataset(dataset_name)
            param_strs = []
            for modality, params in beta_params.items():
                param_strs.append(f"{modality}(α={params['alpha_base']},β={params['beta_base']})")
            print(f"==> Beta参数: {' | '.join(param_strs)}")
    
    def save_config(self, output_path: str):
        """
        保存当前配置到文件
        Args:
            output_path: 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
        print(f"配置已保存到: {output_path}")


def merge_args_with_config(args: argparse.Namespace, config_loader: ConfigLoader) -> argparse.Namespace:
    """
    合并命令行参数和配置文件参数
    命令行参数优先级更高
    Args:
        args: 命令行参数
        config_loader: 配置加载器
    Returns:
        合并后的参数
    """
    config_args = config_loader.get_training_args()
    
    # 命令行参数覆盖配置文件参数
    for key, value in vars(args).items():
        if value is not None:  # 只有明确设置的命令行参数才覆盖配置文件
            setattr(config_args, key, value)
    
    return config_args


def create_sample_configs():
    """创建一些示例配置文件"""
    
    # SYSU数据集优化配置
    sysu_config = {
        'training': {
            'dataset': 'sysu',
            'batch_size': 8,
            'lr': 0.1,
            'num_pos': 4
        },
        'hypergraph': {
            'use_beta_distribution': True,
            'use_diagonal': True,
            'graphw': 1.2,
            'edge': 256
        }
    }
    
    # RegDB数据集优化配置
    regdb_config = {
        'training': {
            'dataset': 'regdb',
            'batch_size': 6,
            'lr': 0.08,
            'num_pos': 4
        },
        'hypergraph': {
            'use_beta_distribution': True,
            'use_diagonal': True,
            'graphw': 1.0,
            'edge': 128
        }
    }
    
    # 保存示例配置
    with open('config_sysu.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(sysu_config, f, default_flow_style=False, allow_unicode=True)
    
    with open('config_regdb.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(regdb_config, f, default_flow_style=False, allow_unicode=True)
    
    print("已创建示例配置文件: config_sysu.yaml, config_regdb.yaml")


if __name__ == "__main__":
    # 测试配置加载器
    try:
        loader = ConfigLoader('config.yaml')
        loader.print_config_summary('sysu')
        
        # 测试实验配置
        loader.apply_experiment_config('beta_only')
        print("应用beta_only配置后:")
        loader.print_config_summary('sysu')
        
    except FileNotFoundError as e:
        print(f"错误: {e}")
        print("请确保config.yaml文件存在")
