#!/usr/bin/env python3
"""
在现有模型中添加特征钩子来捕获BN后和超图卷积后的特征
这是最简单的方法，只需要修改model.py中的几行代码
"""

import torch
import numpy as np
import matplotlib
# 设置matplotlib后端为非交互式，避免图形界面问题
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
import os

# 导入字体配置
try:
    from font_config import setup_matplotlib_fonts, get_english_labels
    setup_matplotlib_fonts()
    FONT_CONFIGURED = True
except ImportError:
    # 如果没有font_config模块，使用基础配置
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'Liberation Sans']
    plt.rcParams['axes.unicode_minus'] = False
    FONT_CONFIGURED = False

# 全局变量存储特征
captured_features = {
    'bn_features': [],
    'graph_features': [],
    'labels': [],
    'part_indices': []
}

def clear_captured_features():
    """清空捕获的特征"""
    global captured_features
    captured_features = {
        'bn_features': [],
        'graph_features': [],
        'labels': [],
        'part_indices': []
    }

def save_features_for_visualization(bn_feat, graph_feat, part_idx, label=None):
    """
    保存特征用于可视化
    这个函数需要在model.py的forward函数中调用

    Args:
        bn_feat: BN后的特征 [B, C, H, W]
        graph_feat: 超图卷积后的特征 [B, C, H, W]
        part_idx: part索引
        label: 标签（可选）
    """
    global captured_features

    # 只在评估模式下保存特征，避免训练时内存溢出
    if bn_feat.requires_grad:
        return

    try:
        # 池化并展平特征
        bn_pooled = torch.nn.functional.avg_pool2d(bn_feat, bn_feat.size()[2:])
        bn_pooled = bn_pooled.view(bn_pooled.size(0), -1)

        graph_pooled = torch.nn.functional.avg_pool2d(graph_feat, graph_feat.size()[2:])
        graph_pooled = graph_pooled.view(graph_pooled.size(0), -1)

        batch_size = bn_pooled.size(0)

        # 转换为numpy并保存
        captured_features['bn_features'].append(bn_pooled.detach().cpu().numpy())
        captured_features['graph_features'].append(graph_pooled.detach().cpu().numpy())
        captured_features['part_indices'].extend([part_idx] * batch_size)

        # 处理标签
        if label is not None:
            if isinstance(label, torch.Tensor):
                if label.numel() == 1:
                    # 单个标签，复制到整个batch
                    label_value = label.item()
                    captured_features['labels'].extend([label_value] * batch_size)
                else:
                    # 多个标签
                    label_list = label.cpu().numpy().tolist()
                    if len(label_list) == batch_size:
                        captured_features['labels'].extend(label_list)
                    else:
                        # 标签数量不匹配，使用第一个标签
                        captured_features['labels'].extend([label_list[0]] * batch_size)
            elif np.isscalar(label):
                captured_features['labels'].extend([label] * batch_size)
            else:
                # 其他情况，转换为列表
                if hasattr(label, '__len__') and len(label) == batch_size:
                    captured_features['labels'].extend(list(label))
                else:
                    captured_features['labels'].extend([0] * batch_size)
        else:
            captured_features['labels'].extend([0] * batch_size)

    except Exception as e:
        print(f"保存特征时出错: {e}")
        print(f"BN特征形状: {bn_feat.shape}")
        print(f"超图特征形状: {graph_feat.shape}")
        print(f"Part索引: {part_idx}")
        print(f"标签: {label}")

def analyze_captured_features():
    """分析捕获的特征"""
    global captured_features

    if not captured_features['bn_features']:
        print("没有捕获到特征数据")
        return None

    # 转换为numpy数组
    bn_features = np.vstack(captured_features['bn_features'])
    graph_features = np.vstack(captured_features['graph_features'])
    labels = np.array(captured_features['labels'])
    part_indices = np.array(captured_features['part_indices'])

    # 确保所有数组长度一致
    min_length = min(len(bn_features), len(graph_features), len(labels), len(part_indices))
    if min_length < len(bn_features):
        print(f"警告: 数组长度不一致，截断到 {min_length}")
        bn_features = bn_features[:min_length]
        graph_features = graph_features[:min_length]
        labels = labels[:min_length]
        part_indices = part_indices[:min_length]
    
    print(f"捕获的特征数量: {len(bn_features)}")
    print(f"特征维度: {bn_features.shape[1]}")
    print(f"标签数量: {len(np.unique(labels))}")
    print(f"Part数量: {len(np.unique(part_indices))}")
    print(f"BN特征形状: {bn_features.shape}")
    print(f"超图特征形状: {graph_features.shape}")
    print(f"标签数组长度: {len(labels)}")
    print(f"Part索引数组长度: {len(part_indices)}")
    
    # 计算统计信息
    stats = {}
    
    # 特征范数
    bn_norms = np.linalg.norm(bn_features, axis=1)
    graph_norms = np.linalg.norm(graph_features, axis=1)
    
    stats['bn_norm_mean'] = bn_norms.mean()
    stats['bn_norm_std'] = bn_norms.std()
    stats['graph_norm_mean'] = graph_norms.mean()
    stats['graph_norm_std'] = graph_norms.std()
    
    print(f"BN特征范数: 均值={stats['bn_norm_mean']:.4f}, 标准差={stats['bn_norm_std']:.4f}")
    print(f"超图特征范数: 均值={stats['graph_norm_mean']:.4f}, 标准差={stats['graph_norm_std']:.4f}")
    
    # 余弦相似度
    similarities = []
    for i in range(len(bn_features)):
        sim = np.dot(bn_features[i], graph_features[i]) / (
            np.linalg.norm(bn_features[i]) * np.linalg.norm(graph_features[i]) + 1e-8
        )
        similarities.append(sim)
    
    similarities = np.array(similarities)
    stats['similarity_mean'] = similarities.mean()
    stats['similarity_std'] = similarities.std()
    
    print(f"余弦相似度: 均值={stats['similarity_mean']:.4f}, 标准差={stats['similarity_std']:.4f}")
    
    return stats, bn_features, graph_features, labels, part_indices

def visualize_captured_features(save_dir='feature_vis'):
    """可视化捕获的特征"""
    stats, bn_features, graph_features, labels, part_indices = analyze_captured_features()
    
    if stats is None:
        return
    
    os.makedirs(save_dir, exist_ok=True)
    
    # 1. Feature Distribution Comparison
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))

    # Norm distribution
    bn_norms = np.linalg.norm(bn_features, axis=1)
    graph_norms = np.linalg.norm(graph_features, axis=1)

    axes[0, 0].hist(bn_norms, bins=30, alpha=0.7, label='BN Features', color='blue')
    axes[0, 0].hist(graph_norms, bins=30, alpha=0.7, label='Graph Features', color='red')
    axes[0, 0].set_title('Feature Norm Distribution')
    axes[0, 0].set_xlabel('L2 Norm')
    axes[0, 0].legend()

    # Similarity distribution
    similarities = []
    for i in range(len(bn_features)):
        sim = np.dot(bn_features[i], graph_features[i]) / (
            np.linalg.norm(bn_features[i]) * np.linalg.norm(graph_features[i]) + 1e-8
        )
        similarities.append(sim)

    axes[0, 1].hist(similarities, bins=30, alpha=0.7, color='green')
    axes[0, 1].set_title('Cosine Similarity Distribution')
    axes[0, 1].set_xlabel('Cosine Similarity')

    # Feature mean comparison
    bn_mean = bn_features.mean(axis=0)
    graph_mean = graph_features.mean(axis=0)
    dim_to_show = min(50, len(bn_mean))

    axes[1, 0].plot(bn_mean[:dim_to_show], label='BN Features', color='blue')
    axes[1, 0].plot(graph_mean[:dim_to_show], label='Graph Features', color='red')
    axes[1, 0].set_title(f'Feature Mean Comparison (First {dim_to_show} dims)')
    axes[1, 0].legend()

    # Feature difference
    diff = np.abs(bn_features - graph_features).mean(axis=0)
    axes[1, 1].plot(diff[:dim_to_show], color='purple')
    axes[1, 1].set_title(f'Feature Difference (First {dim_to_show} dims)')
    axes[1, 1].set_xlabel('Feature Dimension')
    axes[1, 1].set_ylabel('Mean Absolute Difference')
    
    plt.tight_layout()
    plt.savefig(f'{save_dir}/feature_statistics.png', dpi=300, bbox_inches='tight')
    plt.close()  # 关闭图形，释放内存
    
    # 2. t-SNE Visualization (if enough data)
    if len(bn_features) > 50:
        print("Generating t-SNE visualization...")

        # Random sampling - fix index out of bounds issue
        n_vis = min(500, len(bn_features))
        if n_vis < len(bn_features):
            indices = np.random.choice(len(bn_features), n_vis, replace=False)
            bn_vis = bn_features[indices]
            graph_vis = graph_features[indices]
            labels_vis = labels[indices]
        else:
            # If not enough samples, use all data
            bn_vis = bn_features
            graph_vis = graph_features
            labels_vis = labels

        # t-SNE dimensionality reduction
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(30, n_vis//4))
        bn_2d = tsne.fit_transform(bn_vis)
        graph_2d = tsne.fit_transform(graph_vis)

        # Plot
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))

        unique_labels = np.unique(labels_vis)[:10]  # Show only first 10 classes
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_labels)))

        for i, label in enumerate(unique_labels):
            mask = labels_vis == label
            if np.any(mask):
                axes[0].scatter(bn_2d[mask, 0], bn_2d[mask, 1],
                               c=[colors[i]], label=f'ID {label}', alpha=0.7, s=20)
                axes[1].scatter(graph_2d[mask, 0], graph_2d[mask, 1],
                               c=[colors[i]], label=f'ID {label}', alpha=0.7, s=20)

        axes[0].set_title('BN Features (t-SNE)')
        axes[1].set_title('Hypergraph Features (t-SNE)')
        axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        plt.tight_layout()
        plt.savefig(f'{save_dir}/feature_tsne.png', dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形，释放内存
    
    print(f"可视化结果已保存到 {save_dir}/")
    return stats

def print_model_modification_instructions():
    """打印模型修改说明"""
    print("""
=== 模型修改说明 ===

要启用特征捕获，需要在 model.py 的 forward 函数中添加以下代码：

1. 在文件开头添加导入：
   from add_feature_hooks import save_features_for_visualization

2. 在 forward 函数中，找到这两行代码：
   x = self.bn_conv_reduce(x)  # add a BN
   feat = self.graphw * self.hypergraph(x)

3. 在这两行之间添加：
   # 保存特征用于可视化分析
   if not self.training and hasattr(self, '_capture_features') and self._capture_features:
       save_features_for_visualization(x, feat, i, identities)

4. 在测试前设置捕获标志：
   net._capture_features = True

5. 运行测试后调用可视化：
   from add_feature_hooks import visualize_captured_features
   visualize_captured_features()

完整的修改示例：
```python
# 在 model.py 的 forward 函数中
for i in range(self.part_num):
    mask = masks[:, i:i+1, :, :]
    x = mask*global_feat
    x = self.conv_reduce(x)
    x = self.bn_conv_reduce(x)  # add a BN
    
    # 超图卷积
    feat = self.graphw * self.hypergraph(x)
    
    # 保存特征用于可视化分析
    if not self.training and hasattr(self, '_capture_features') and self._capture_features:
        save_features_for_visualization(x, feat, i, identities)
    
    # 继续后续处理...
```
""")

if __name__ == "__main__":
    print_model_modification_instructions()
