#!/usr/bin/env python3
"""
测试跨模态超图融合的修复
"""

import torch
from cross_modal_hypergraph_fusion import CrossModalHypergraphFusion

def test_hypergraph_fusion_dimensions():
    """测试超图融合的维度是否正确"""
    print("=== 测试跨模态超图融合维度修复 ===")
    
    # 模拟真实的训练参数
    batch_size = 4  # 每个模态4个样本
    feat_dim = 256  # 修正：应该是style_dim（256），不是pool_dim（3072）
    num_strips = 4
    fusion_dim = 128
    num_heads = 4
    H, W = 18, 9
    
    print(f"测试参数:")
    print(f"  batch_size: {batch_size}")
    print(f"  feat_dim: {feat_dim}")
    print(f"  fusion_dim: {fusion_dim}")
    print(f"  num_strips: {num_strips}")
    
    # 创建模拟数据
    features = torch.randn(4 * batch_size, feat_dim, H, W)  # [16, 256, 18, 9]
    identities = torch.randint(0, 10, (batch_size,))  # [4]
    
    print(f"\n输入数据:")
    print(f"  features shape: {features.shape}")
    print(f"  identities shape: {identities.shape}")
    print(f"  identities: {identities}")
    
    # 创建超图融合模块
    try:
        fusion_module = CrossModalHypergraphFusion(
            feat_dim=feat_dim,
            num_strips=num_strips,
            fusion_dim=fusion_dim,
            num_heads=num_heads
        )
        print(f"\n✅ 超图融合模块创建成功")
    except Exception as e:
        print(f"\n❌ 超图融合模块创建失败: {e}")
        return False
    
    # 测试前向传播
    try:
        enhanced_features, fusion_loss = fusion_module(features, identities)
        
        print(f"\n✅ 前向传播成功!")
        print(f"  输出特征形状: {enhanced_features.shape}")
        print(f"  融合损失: {fusion_loss.item():.6f}")
        
        # 验证输出维度
        expected_shape = features.shape
        if enhanced_features.shape == expected_shape:
            print(f"  ✅ 输出维度正确: {enhanced_features.shape}")
        else:
            print(f"  ❌ 输出维度错误: 期望 {expected_shape}, 实际 {enhanced_features.shape}")
            return False
            
        return True
        
    except Exception as e:
        print(f"\n❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gate_network_dimensions():
    """单独测试门控网络的维度"""
    print("\n=== 测试门控网络维度 ===")
    
    fusion_dim = 128
    batch_size = 16
    
    # 创建门控网络
    import torch.nn as nn
    gate_network = nn.Sequential(
        nn.Linear(fusion_dim * 2, fusion_dim),
        nn.ReLU(),
        nn.Linear(fusion_dim, fusion_dim),
        nn.Sigmoid()
    )
    
    # 测试输入
    avg_like = torch.randn(batch_size, fusion_dim)
    max_like = torch.randn(batch_size, fusion_dim)
    
    # 拼接
    pooled_concat = torch.cat([avg_like, max_like], dim=1)  # [16, 256]
    
    print(f"门控网络输入形状: {pooled_concat.shape}")
    
    try:
        gate_weights = gate_network(pooled_concat)
        print(f"✅ 门控网络输出形状: {gate_weights.shape}")
        print(f"  门控权重范围: [{gate_weights.min().item():.3f}, {gate_weights.max().item():.3f}]")
        return True
    except Exception as e:
        print(f"❌ 门控网络测试失败: {e}")
        return False

def test_different_batch_sizes():
    """测试不同批次大小"""
    print("\n=== 测试不同批次大小 ===")
    
    feat_dim = 256  # 修正为正确的维度
    fusion_dim = 128
    H, W = 18, 9
    
    test_cases = [
        {"batch_size": 2, "description": "小批次"},
        {"batch_size": 4, "description": "中等批次"},
        {"batch_size": 8, "description": "大批次"},
    ]
    
    for case in test_cases:
        batch_size = case["batch_size"]
        description = case["description"]
        
        print(f"\n测试 {description} (batch_size={batch_size}):")
        
        features = torch.randn(4 * batch_size, feat_dim, H, W)
        identities = torch.randint(0, 10, (batch_size,))
        
        fusion_module = CrossModalHypergraphFusion(
            feat_dim=feat_dim,
            num_strips=4,
            fusion_dim=fusion_dim,
            num_heads=4
        )
        
        try:
            enhanced_features, fusion_loss = fusion_module(features, identities)
            print(f"  ✅ 成功: 输出 {enhanced_features.shape}, 损失 {fusion_loss.item():.6f}")
        except Exception as e:
            print(f"  ❌ 失败: {e}")
            return False
    
    return True

def main():
    """运行所有测试"""
    print("🔧 跨模态超图融合修复测试")
    print("=" * 50)
    
    # 运行测试
    test1 = test_hypergraph_fusion_dimensions()
    test2 = test_gate_network_dimensions()
    test3 = test_different_batch_sizes()
    
    print("\n" + "=" * 50)
    if all([test1, test2, test3]):
        print("🎉 所有测试通过！维度问题已修复。")
        print("\n修复总结:")
        print("1. ✅ 门控网络维度匹配 (fusion_dim * 2 → fusion_dim)")
        print("2. ✅ 门控融合在正确位置应用 (投影后)")
        print("3. ✅ 支持不同批次大小")
        print("4. ✅ 输出维度保持一致")
        print("\n现在可以正常训练了！")
    else:
        print("❌ 部分测试失败，需要进一步检查。")
    
    print("\n建议:")
    print("- 在实际训练中监控融合损失的变化")
    print("- 观察门控权重的分布，了解模型的学习模式")
    print("- 可以调整fusion_dim来平衡性能和计算效率")

if __name__ == "__main__":
    main()
