#!/usr/bin/env python3
"""
测试修复后的特征可视化
"""

import torch
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 设置非交互式后端
import matplotlib.pyplot as plt
from PIL import Image
import os

def test_data_loading():
    """测试数据加载修复"""
    print("=== 测试数据加载修复 ===")
    
    # 模拟data_manager返回的数据格式
    fake_img_paths = [
        '/fake/path/img1.jpg',
        '/fake/path/img2.jpg', 
        '/fake/path/img3.jpg'
    ]
    fake_labels = np.array([1, 2, 3])
    
    print(f"模拟图像路径: {fake_img_paths}")
    print(f"模拟标签: {fake_labels}")
    
    # 测试PIL图像加载（使用随机图像）
    try:
        # 创建测试图像
        test_img = Image.new('RGB', (192, 384), color='red')
        test_img.save('test_image.jpg')
        
        # 测试加载
        img = Image.open('test_image.jpg').convert('RGB')
        print(f"✅ 图像加载成功: {img.size}, 模式: {img.mode}")
        
        # 清理
        os.remove('test_image.jpg')
        
    except Exception as e:
        print(f"❌ 图像加载失败: {e}")
    
    return True

def test_matplotlib_backend():
    """测试matplotlib后端设置"""
    print("\n=== 测试matplotlib后端 ===")
    
    try:
        # 检查后端
        backend = matplotlib.get_backend()
        print(f"当前matplotlib后端: {backend}")
        
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(8, 6))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        ax.plot(x, y, label='Test Line')
        ax.set_title('Backend Test')
        ax.set_xlabel('X axis')
        ax.set_ylabel('Y axis')
        ax.legend()
        
        # 保存图表（不显示）
        plt.savefig('backend_test.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ matplotlib后端测试成功，图表已保存为 backend_test.png")
        
        # 检查文件是否存在
        if os.path.exists('backend_test.png'):
            print("✅ 图片文件创建成功")
            # 清理
            os.remove('backend_test.png')
        else:
            print("❌ 图片文件未创建")
            
    except Exception as e:
        print(f"❌ matplotlib后端测试失败: {e}")
    
    return True

def test_feature_visualization_simulation():
    """模拟特征可视化过程"""
    print("\n=== 模拟特征可视化过程 ===")
    
    try:
        # 生成模拟特征数据
        np.random.seed(42)
        n_samples = 100
        n_features = 256
        
        # BN特征
        bn_features = np.random.randn(n_samples, n_features)
        # 超图特征（稍微不同）
        graph_features = bn_features + 0.1 * np.random.randn(n_samples, n_features)
        
        print(f"生成模拟数据: BN特征 {bn_features.shape}, 超图特征 {graph_features.shape}")
        
        # 计算统计信息
        bn_norms = np.linalg.norm(bn_features, axis=1)
        graph_norms = np.linalg.norm(graph_features, axis=1)
        
        similarities = []
        for i in range(len(bn_features)):
            sim = np.dot(bn_features[i], graph_features[i]) / (
                np.linalg.norm(bn_features[i]) * np.linalg.norm(graph_features[i]) + 1e-8
            )
            similarities.append(sim)
        
        similarities = np.array(similarities)
        
        print(f"BN特征范数: 均值={bn_norms.mean():.4f}, 标准差={bn_norms.std():.4f}")
        print(f"超图特征范数: 均值={graph_norms.mean():.4f}, 标准差={graph_norms.std():.4f}")
        print(f"余弦相似度: 均值={similarities.mean():.4f}, 标准差={similarities.std():.4f}")
        
        # 创建可视化
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 1. 范数分布
        axes[0, 0].hist(bn_norms, bins=20, alpha=0.7, label='BN Features', color='blue')
        axes[0, 0].hist(graph_norms, bins=20, alpha=0.7, label='Graph Features', color='red')
        axes[0, 0].set_title('Feature Norm Distribution')
        axes[0, 0].legend()
        
        # 2. 相似度分布
        axes[0, 1].hist(similarities, bins=20, alpha=0.7, color='green')
        axes[0, 1].set_title('Cosine Similarity Distribution')
        
        # 3. 特征均值
        bn_mean = bn_features.mean(axis=0)
        graph_mean = graph_features.mean(axis=0)
        axes[1, 0].plot(bn_mean[:50], label='BN Features', color='blue')
        axes[1, 0].plot(graph_mean[:50], label='Graph Features', color='red')
        axes[1, 0].set_title('Feature Mean Comparison')
        axes[1, 0].legend()
        
        # 4. 特征差异
        diff = np.abs(bn_features - graph_features).mean(axis=0)
        axes[1, 1].plot(diff[:50], color='purple')
        axes[1, 1].set_title('Feature Difference')
        
        plt.tight_layout()
        plt.savefig('simulation_test.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ 特征可视化模拟成功，图表已保存为 simulation_test.png")
        
        # 清理
        if os.path.exists('simulation_test.png'):
            os.remove('simulation_test.png')
            
    except Exception as e:
        print(f"❌ 特征可视化模拟失败: {e}")
    
    return True

def main():
    """运行所有测试"""
    print("🔧 测试特征可视化修复")
    print("=" * 50)
    
    # 运行测试
    test1 = test_data_loading()
    test2 = test_matplotlib_backend()
    test3 = test_feature_visualization_simulation()
    
    print("\n" + "=" * 50)
    if all([test1, test2, test3]):
        print("🎉 所有测试通过！修复成功。")
        print("\n现在可以运行:")
        print("  python run_feature_visualization.py")
        print("\n图表将保存为PNG文件，不会尝试显示图形界面。")
    else:
        print("❌ 部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
