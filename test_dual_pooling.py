#!/usr/bin/env python3
"""
测试双重池化（平均池化+最大池化）的效果
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

def test_dual_pooling_effect():
    """测试双重池化的效果"""
    print("=== 测试双重池化效果 ===")
    
    # 创建测试数据
    batch_size = 4
    channels = 256
    height, width = 18, 9
    
    # 创建具有不同特性的特征图
    features = torch.randn(batch_size, channels, height, width)
    
    # 添加一些显著的局部特征（模拟关键特征点）
    features[0, :, 5:8, 3:6] += 3.0  # 强局部特征
    features[1, :, 10:13, 1:4] += 2.5
    features[2, :, 2:5, 6:9] += 4.0
    features[3, :, 15:18, 2:5] += 3.5
    
    print(f"输入特征形状: {features.shape}")
    
    # 测试不同的池化方法
    num_strips = 4
    strip_height = height // num_strips
    
    results = {
        'avg_only': [],
        'max_only': [], 
        'simple_add': [],
        'weighted_add': []
    }
    
    # 可学习权重（模拟训练后的值）
    avg_weight = 0.6
    max_weight = 0.4
    
    for i in range(num_strips):
        start_h = i * strip_height
        end_h = (i + 1) * strip_height if i < num_strips - 1 else height
        
        # 提取条带
        strip = features[:, :, start_h:end_h, :]  # [B, C, strip_h, W]
        
        # 1. 仅平均池化
        avg_pooled = F.adaptive_avg_pool2d(strip, (1, 1)).squeeze(-1).squeeze(-1)
        results['avg_only'].append(avg_pooled)
        
        # 2. 仅最大池化
        max_pooled = F.adaptive_max_pool2d(strip, (1, 1)).squeeze(-1).squeeze(-1)
        results['max_only'].append(max_pooled)
        
        # 3. 简单相加
        simple_add = avg_pooled + max_pooled
        results['simple_add'].append(simple_add)
        
        # 4. 加权相加
        weighted_add = avg_weight * avg_pooled + max_weight * max_pooled
        results['weighted_add'].append(weighted_add)
    
    # 分析不同方法的特征统计
    print("\n=== 特征统计分析 ===")
    
    for method, strips in results.items():
        all_features = torch.stack(strips, dim=1)  # [B, num_strips, C]
        
        # 计算统计信息
        mean_val = all_features.mean().item()
        std_val = all_features.std().item()
        max_val = all_features.max().item()
        min_val = all_features.min().item()
        
        print(f"{method:12s}: 均值={mean_val:6.3f}, 标准差={std_val:6.3f}, "
              f"最大值={max_val:6.3f}, 最小值={min_val:6.3f}")
    
    # 分析特征的判别性
    print("\n=== 判别性分析 ===")
    
    for method, strips in results.items():
        all_features = torch.stack(strips, dim=1)  # [B, num_strips, C]
        
        # 计算样本间的相似度差异
        features_flat = all_features.view(batch_size, -1)  # [B, num_strips*C]
        features_norm = F.normalize(features_flat, p=2, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.mm(features_norm, features_norm.t())
        
        # 计算对角线外元素的平均相似度（样本间相似度）
        mask = torch.eye(batch_size) == 0
        inter_sample_similarity = similarity_matrix[mask].mean().item()
        
        print(f"{method:12s}: 样本间平均相似度={inter_sample_similarity:.4f} "
              f"(越低表示判别性越好)")
    
    return results

def visualize_pooling_comparison():
    """可视化不同池化方法的效果"""
    print("\n=== 生成可视化对比 ===")
    
    # 创建一个具有明显局部特征的测试样本
    channels = 64  # 减少通道数便于可视化
    height, width = 18, 9
    
    # 创建基础特征图
    feature_map = torch.randn(1, channels, height, width) * 0.5
    
    # 添加显著的局部特征
    feature_map[0, :, 2:5, 2:4] += 2.0    # 上部强特征
    feature_map[0, :, 8:11, 5:7] += 3.0   # 中部强特征  
    feature_map[0, :, 14:17, 1:3] += 2.5  # 下部强特征
    
    num_strips = 4
    strip_height = height // num_strips
    
    # 存储每个条带的池化结果
    avg_results = []
    max_results = []
    fused_results = []
    
    for i in range(num_strips):
        start_h = i * strip_height
        end_h = (i + 1) * strip_height if i < num_strips - 1 else height
        
        strip = feature_map[:, :, start_h:end_h, :]
        
        # 不同池化方法
        avg_pooled = F.adaptive_avg_pool2d(strip, (1, 1)).squeeze()  # [C]
        max_pooled = F.adaptive_max_pool2d(strip, (1, 1)).squeeze()  # [C]
        fused = 0.6 * avg_pooled + 0.4 * max_pooled  # [C]
        
        avg_results.append(avg_pooled.numpy())
        max_results.append(max_pooled.numpy())
        fused_results.append(fused.numpy())
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. 原始特征图的平均值（跨通道）
    original_mean = feature_map.squeeze().mean(dim=0).numpy()  # [H, W]
    im1 = axes[0, 0].imshow(original_mean, cmap='viridis', aspect='auto')
    axes[0, 0].set_title('Original Feature Map (Channel Mean)')
    axes[0, 0].set_xlabel('Width')
    axes[0, 0].set_ylabel('Height')
    plt.colorbar(im1, ax=axes[0, 0])
    
    # 添加条带分割线
    for i in range(1, num_strips):
        y = i * strip_height - 0.5
        axes[0, 0].axhline(y, color='red', linestyle='--', alpha=0.7)
    
    # 2. 平均池化结果
    avg_array = np.array(avg_results)  # [num_strips, C]
    im2 = axes[0, 1].imshow(avg_array.T, cmap='viridis', aspect='auto')
    axes[0, 1].set_title('Average Pooling Results')
    axes[0, 1].set_xlabel('Strip Index')
    axes[0, 1].set_ylabel('Channel Index')
    plt.colorbar(im2, ax=axes[0, 1])
    
    # 3. 最大池化结果
    max_array = np.array(max_results)  # [num_strips, C]
    im3 = axes[1, 0].imshow(max_array.T, cmap='viridis', aspect='auto')
    axes[1, 0].set_title('Max Pooling Results')
    axes[1, 0].set_xlabel('Strip Index')
    axes[1, 0].set_ylabel('Channel Index')
    plt.colorbar(im3, ax=axes[1, 0])
    
    # 4. 融合池化结果
    fused_array = np.array(fused_results)  # [num_strips, C]
    im4 = axes[1, 1].imshow(fused_array.T, cmap='viridis', aspect='auto')
    axes[1, 1].set_title('Fused Pooling Results (0.6*Avg + 0.4*Max)')
    axes[1, 1].set_xlabel('Strip Index')
    axes[1, 1].set_ylabel('Channel Index')
    plt.colorbar(im4, ax=axes[1, 1])
    
    plt.tight_layout()
    plt.savefig('dual_pooling_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("可视化结果已保存到 dual_pooling_comparison.png")
    
    # 统计分析
    print(f"\n条带特征统计:")
    for i in range(num_strips):
        print(f"条带 {i}: 平均池化均值={np.mean(avg_results[i]):.3f}, "
              f"最大池化均值={np.mean(max_results[i]):.3f}, "
              f"融合后均值={np.mean(fused_results[i]):.3f}")

def analyze_pooling_benefits():
    """分析双重池化的优势"""
    print("\n=== 双重池化优势分析 ===")
    
    print("1. 信息互补性:")
    print("   - 平均池化：保留全局统计信息，对噪声鲁棒")
    print("   - 最大池化：保留显著特征，突出关键信息")
    print("   - 融合后：兼具全局性和显著性")
    
    print("\n2. 特征表达能力:")
    print("   - 单一池化：信息有限，可能丢失重要特征")
    print("   - 双重池化：更丰富的特征表示")
    
    print("\n3. 可学习权重的作用:")
    print("   - 自适应调节两种池化的重要性")
    print("   - 根据任务需求优化特征组合")
    
    print("\n4. 在跨模态融合中的价值:")
    print("   - 更好地捕获跨模态的共同特征")
    print("   - 增强特征的判别性和鲁棒性")

def main():
    """运行所有测试"""
    print("🔍 双重池化效果测试")
    print("=" * 50)
    
    # 测试双重池化效果
    results = test_dual_pooling_effect()
    
    # 生成可视化
    visualize_pooling_comparison()
    
    # 分析优势
    analyze_pooling_benefits()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n建议:")
    print("1. 双重池化确实能提供更丰富的特征表示")
    print("2. 可学习权重让模型自适应调节池化策略")
    print("3. 在跨模态超图融合中应该有积极作用")
    print("4. 可以尝试不同的权重初始化策略")

if __name__ == "__main__":
    main()
