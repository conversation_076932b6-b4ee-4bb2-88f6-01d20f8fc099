#!/usr/bin/env python3
"""
特征可视化对比实验
比较BN后特征和超图卷积后特征的差异
"""

import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import os
from model import embed_net
from config_loader import ConfigLoader
from data_loader import SYSUData
import torchvision.transforms as transforms

class FeatureVisualizer:
    def __init__(self, model, device='cuda'):
        self.model = model
        self.device = device
        self.features_bn = []
        self.features_graph = []
        self.labels = []
        self.modalities = []
        
    def extract_features(self, dataloader, max_samples=500):
        """
        提取特征进行可视化
        Args:
            dataloader: 数据加载器
            max_samples: 最大样本数量
        """
        self.model.eval()
        sample_count = 0
        
        with torch.no_grad():
            for batch_idx, (input, label) in enumerate(dataloader):
                if sample_count >= max_samples:
                    break
                    
                input = input.to(self.device)
                batch_size = input.size(0)
                
                # 修改模型以捕获中间特征
                features_bn_batch = []
                features_graph_batch = []
                
                # 前向传播到特征提取部分
                x1_1, x1_2, x2_1, x2_2 = input, input, input, input
                modal = 0  # 使用可见光模式
                
                # 特征提取
                x1_1 = self.model.visible_module(x1_1)
                x2_1 = self.model.thermal_module(x2_1)
                x1_2 = self.model.visible_moduleA(x1_2)
                x2_2 = self.model.thermal_moduleA(x2_2)
                x = torch.cat((x1_1, x1_2, x2_1, x2_2), 0)
                
                # 共享层
                x_low = x
                x = self.model.base_resnet.base.layer1(x)
                x = self.model.SSM1(x, x_low)
                x_low = x
                x = self.model.base_resnet.base.layer2(x)
                x = self.model.SSM2(x, x_low)
                x = self.model.base_resnet.base.layer3(x)
                x = self.model.base_resnet.base.layer4(x)
                global_feat = x
                
                # 生成注意力掩码
                masks = self.model.spatial_attention(global_feat)
                masks = self.model.activation(masks)
                
                # 对每个part提取特征
                for i in range(self.model.part_num):
                    mask = masks[:, i:i+1, :, :]
                    x = mask * global_feat
                    x = self.model.conv_reduce(x)
                    
                    # BN后的特征
                    x_bn = self.model.bn_conv_reduce(x)
                    features_bn_batch.append(x_bn.clone())
                    
                    # 超图卷积后的特征
                    if hasattr(self.model.hypergraph, 'use_beta_distribution') and self.model.hypergraph.use_beta_distribution:
                        feat_graph = self.model.graphw * self.model.hypergraph(x_bn, 'visible_original')
                    else:
                        feat_graph = self.model.graphw * self.model.hypergraph(x_bn)
                    features_graph_batch.append(feat_graph.clone())
                
                # 平均池化并展平特征
                for part_idx in range(self.model.part_num):
                    feat_bn = F.avg_pool2d(features_bn_batch[part_idx], features_bn_batch[part_idx].size()[2:])
                    feat_bn = feat_bn.view(feat_bn.size(0), -1)
                    
                    feat_graph = F.avg_pool2d(features_graph_batch[part_idx], features_graph_batch[part_idx].size()[2:])
                    feat_graph = feat_graph.view(feat_graph.size(0), -1)
                    
                    self.features_bn.append(feat_bn.cpu().numpy())
                    self.features_graph.append(feat_graph.cpu().numpy())
                    
                    # 扩展标签和模态信息
                    self.labels.extend([label[i % batch_size].item() for i in range(feat_bn.size(0))])
                    self.modalities.extend([f'part_{part_idx}'] * feat_bn.size(0))
                
                sample_count += batch_size
                if batch_idx % 10 == 0:
                    print(f'已处理 {sample_count} 个样本')
        
        # 转换为numpy数组
        self.features_bn = np.vstack(self.features_bn)
        self.features_graph = np.vstack(self.features_graph)
        self.labels = np.array(self.labels)
        self.modalities = np.array(self.modalities)
        
        print(f'特征提取完成: BN特征 {self.features_bn.shape}, 超图特征 {self.features_graph.shape}')
    
    def compute_feature_statistics(self):
        """计算特征统计信息"""
        print("\n=== 特征统计信息 ===")
        
        # 特征范数
        bn_norm = np.linalg.norm(self.features_bn, axis=1)
        graph_norm = np.linalg.norm(self.features_graph, axis=1)
        
        print(f"BN特征范数: 均值={bn_norm.mean():.4f}, 标准差={bn_norm.std():.4f}")
        print(f"超图特征范数: 均值={graph_norm.mean():.4f}, 标准差={graph_norm.std():.4f}")
        
        # 特征相似度
        similarities = []
        for i in range(len(self.features_bn)):
            sim = np.dot(self.features_bn[i], self.features_graph[i]) / (
                np.linalg.norm(self.features_bn[i]) * np.linalg.norm(self.features_graph[i]) + 1e-8
            )
            similarities.append(sim)
        
        similarities = np.array(similarities)
        print(f"BN与超图特征余弦相似度: 均值={similarities.mean():.4f}, 标准差={similarities.std():.4f}")
        
        return {
            'bn_norm_mean': bn_norm.mean(),
            'bn_norm_std': bn_norm.std(),
            'graph_norm_mean': graph_norm.mean(),
            'graph_norm_std': graph_norm.std(),
            'similarity_mean': similarities.mean(),
            'similarity_std': similarities.std()
        }
    
    def visualize_features_2d(self, method='tsne', n_samples=1000, save_dir='feature_vis'):
        """
        2D特征可视化
        Args:
            method: 'tsne' 或 'pca'
            n_samples: 可视化的样本数量
            save_dir: 保存目录
        """
        os.makedirs(save_dir, exist_ok=True)
        
        # 随机采样
        if len(self.features_bn) > n_samples:
            indices = np.random.choice(len(self.features_bn), n_samples, replace=False)
            feat_bn_vis = self.features_bn[indices]
            feat_graph_vis = self.features_graph[indices]
            labels_vis = self.labels[indices]
        else:
            feat_bn_vis = self.features_bn
            feat_graph_vis = self.features_graph
            labels_vis = self.labels
        
        # 降维
        if method == 'tsne':
            reducer = TSNE(n_components=2, random_state=42, perplexity=30)
        else:
            reducer = PCA(n_components=2, random_state=42)
        
        print(f"使用 {method.upper()} 进行降维...")
        
        # BN特征降维
        feat_bn_2d = reducer.fit_transform(feat_bn_vis)
        
        # 超图特征降维
        feat_graph_2d = reducer.fit_transform(feat_graph_vis)
        
        # 绘制对比图
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # 选择前10个类别进行可视化
        unique_labels = np.unique(labels_vis)[:10]
        colors = plt.cm.tab10(np.linspace(0, 1, len(unique_labels)))
        
        # BN特征可视化
        for i, label in enumerate(unique_labels):
            mask = labels_vis == label
            axes[0].scatter(feat_bn_2d[mask, 0], feat_bn_2d[mask, 1], 
                           c=[colors[i]], label=f'ID {label}', alpha=0.6, s=20)
        axes[0].set_title(f'BN后特征 ({method.upper()})')
        axes[0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        # 超图特征可视化
        for i, label in enumerate(unique_labels):
            mask = labels_vis == label
            axes[1].scatter(feat_graph_2d[mask, 0], feat_graph_2d[mask, 1], 
                           c=[colors[i]], label=f'ID {label}', alpha=0.6, s=20)
        axes[1].set_title(f'超图卷积后特征 ({method.upper()})')
        axes[1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        
        plt.tight_layout()
        plt.savefig(f'{save_dir}/feature_comparison_{method}.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"特征可视化已保存到 {save_dir}/feature_comparison_{method}.png")
    
    def visualize_feature_distribution(self, save_dir='feature_vis'):
        """可视化特征分布"""
        os.makedirs(save_dir, exist_ok=True)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 特征范数分布
        bn_norms = np.linalg.norm(self.features_bn, axis=1)
        graph_norms = np.linalg.norm(self.features_graph, axis=1)
        
        axes[0, 0].hist(bn_norms, bins=50, alpha=0.7, label='BN特征', color='blue')
        axes[0, 0].hist(graph_norms, bins=50, alpha=0.7, label='超图特征', color='red')
        axes[0, 0].set_title('特征范数分布')
        axes[0, 0].set_xlabel('L2范数')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].legend()
        
        # 特征维度均值分布
        bn_mean = self.features_bn.mean(axis=0)
        graph_mean = self.features_graph.mean(axis=0)
        
        axes[0, 1].plot(bn_mean, label='BN特征均值', color='blue')
        axes[0, 1].plot(graph_mean, label='超图特征均值', color='red')
        axes[0, 1].set_title('各维度特征均值')
        axes[0, 1].set_xlabel('特征维度')
        axes[0, 1].set_ylabel('均值')
        axes[0, 1].legend()
        
        # 特征相似度分布
        similarities = []
        for i in range(len(self.features_bn)):
            sim = np.dot(self.features_bn[i], self.features_graph[i]) / (
                np.linalg.norm(self.features_bn[i]) * np.linalg.norm(self.features_graph[i]) + 1e-8
            )
            similarities.append(sim)
        
        axes[1, 0].hist(similarities, bins=50, alpha=0.7, color='green')
        axes[1, 0].set_title('BN与超图特征余弦相似度分布')
        axes[1, 0].set_xlabel('余弦相似度')
        axes[1, 0].set_ylabel('频次')
        
        # 特征差异热图
        diff_matrix = np.corrcoef(self.features_bn[:100].T, self.features_graph[:100].T)
        im = axes[1, 1].imshow(diff_matrix, cmap='coolwarm', aspect='auto')
        axes[1, 1].set_title('特征相关性矩阵')
        plt.colorbar(im, ax=axes[1, 1])
        
        plt.tight_layout()
        plt.savefig(f'{save_dir}/feature_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"特征分布可视化已保存到 {save_dir}/feature_distribution.png")


def run_feature_visualization_experiment():
    """运行特征可视化实验"""
    print("=== 特征可视化对比实验 ===")
    
    # 加载配置和模型
    config_loader = ConfigLoader('config.yaml')
    args = config_loader.get_training_args()
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    net = embed_net(
        class_num=395,  # SYSU数据集
        enable_cross_modal=True,
        num_strips=4,
        projection_dim=128,
        num_heads=4,
    )
    
    # 加载检查点
    checkpoint_path = 'result/saved_model/sysu_hsfl_p4_n4_lr_0.1_seed_0_best_gpu1.t'
    if os.path.exists(checkpoint_path):
        print(f"加载检查点: {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)
        
        # 智能加载权重
        model_dict = net.state_dict()
        checkpoint_dict = checkpoint['net']
        filtered_dict = {k: v for k, v in checkpoint_dict.items() 
                        if k in model_dict and model_dict[k].shape == v.shape}
        model_dict.update(filtered_dict)
        net.load_state_dict(model_dict)
        print(f"成功加载 {len(filtered_dict)}/{len(checkpoint_dict)} 个参数")
    
    net.to(device)
    
    # 准备数据
    normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    transform_test = transforms.Compose([
        transforms.ToPILImage(),
        transforms.Resize((384, 192)),
        transforms.ToTensor(),
        normalize,
    ])
    
    # 加载测试数据
    data_path = '../DataSets/SYSU-MM01/'
    if os.path.exists(data_path):
        from data_manager import process_query_sysu
        query_img, query_label, query_cam = process_query_sysu(data_path, mode='all')
        
        # 创建数据加载器（只取少量数据用于可视化）
        from torch.utils.data import DataLoader, TensorDataset
        query_img_tensor = torch.stack([transform_test(img) for img in query_img[:200]])
        query_label_tensor = torch.tensor(query_label[:200])
        
        dataset = TensorDataset(query_img_tensor, query_label_tensor)
        dataloader = DataLoader(dataset, batch_size=8, shuffle=False)
        
        # 创建可视化器
        visualizer = FeatureVisualizer(net, device)
        
        # 提取特征
        print("开始提取特征...")
        visualizer.extract_features(dataloader, max_samples=100)
        
        # 计算统计信息
        stats = visualizer.compute_feature_statistics()
        
        # 可视化
        print("生成可视化图表...")
        visualizer.visualize_features_2d(method='tsne')
        visualizer.visualize_features_2d(method='pca')
        visualizer.visualize_feature_distribution()
        
        print("实验完成！")
        return stats
    else:
        print(f"数据路径不存在: {data_path}")
        return None


if __name__ == "__main__":
    run_feature_visualization_experiment()
