#!/usr/bin/env python3
"""
快速验证维度修复
"""

import torch
import torch.nn as nn

def test_dimension_fix():
    """测试维度修复是否正确"""
    print("=== 快速维度测试 ===")
    
    # 模拟实际参数
    batch_size = 4
    feat_dim = 256  # style_dim
    fusion_dim = 128
    H, W = 18, 9
    num_strips = 4
    
    print(f"测试参数: feat_dim={feat_dim}, fusion_dim={fusion_dim}")
    
    # 1. 测试特征投影层
    feature_projection = nn.Linear(feat_dim, fusion_dim)
    
    # 模拟条带特征
    strips = torch.randn(4 * batch_size, num_strips, feat_dim)  # [16, 4, 256]
    print(f"条带特征形状: {strips.shape}")
    
    try:
        projected = feature_projection(strips)
        print(f"✅ 特征投影成功: {strips.shape} → {projected.shape}")
    except Exception as e:
        print(f"❌ 特征投影失败: {e}")
        return False
    
    # 2. 测试门控网络
    gate_network = nn.Sequential(
        nn.Linear(fusion_dim * 2, fusion_dim),
        nn.<PERSON><PERSON><PERSON>(),
        nn.Linear(fusion_dim, fusion_dim),
        nn.Sigmoid()
    )
    
    # 模拟门控输入
    avg_like = torch.randn(4 * batch_size, fusion_dim)
    max_like = torch.randn(4 * batch_size, fusion_dim)
    gate_input = torch.cat([avg_like, max_like], dim=1)  # [16, 256]
    
    print(f"门控输入形状: {gate_input.shape}")
    
    try:
        gate_weights = gate_network(gate_input)
        print(f"✅ 门控网络成功: {gate_input.shape} → {gate_weights.shape}")
    except Exception as e:
        print(f"❌ 门控网络失败: {e}")
        return False
    
    # 3. 测试完整的CrossModalHypergraphFusion
    try:
        from cross_modal_hypergraph_fusion import CrossModalHypergraphFusion
        
        fusion_module = CrossModalHypergraphFusion(
            feat_dim=feat_dim,
            num_strips=num_strips,
            fusion_dim=fusion_dim,
            num_heads=4
        )
        
        # 模拟输入
        features = torch.randn(4 * batch_size, feat_dim, H, W)
        identities = torch.randint(0, 10, (batch_size,))
        
        print(f"融合模块输入: features={features.shape}, identities={identities.shape}")
        
        enhanced_features, fusion_loss = fusion_module(features, identities)
        
        print(f"✅ 融合模块成功: {features.shape} → {enhanced_features.shape}")
        print(f"   融合损失: {fusion_loss.item():.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 融合模块失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """运行测试"""
    print("🔧 快速维度修复验证")
    print("=" * 30)
    
    success = test_dimension_fix()
    
    print("\n" + "=" * 30)
    if success:
        print("🎉 维度修复成功！")
        print("现在可以正常训练了。")
    else:
        print("❌ 仍有问题需要解决。")
    
    print("\n关键修复:")
    print("- feat_dim: 3072 → 256 (使用style_dim而不是pool_dim)")
    print("- 门控网络在正确位置使用")
    print("- 维度匹配检查通过")

if __name__ == "__main__":
    main()
