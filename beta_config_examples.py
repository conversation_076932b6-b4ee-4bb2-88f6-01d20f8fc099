#!/usr/bin/env python3
"""
Beta分布参数配置示例文件
用于HSFLNet中超边卷积的Beta分布概率连接矩阵生成

使用方法:
1. 直接在train.py命令行中指定参数
2. 修改此文件中的参数值，然后在train.py中导入使用
"""

# ============================================================================
# SYSU-MM01 数据集 Beta分布参数配置
# ============================================================================

SYSU_BETA_PARAMS = {
    # 可见光原图参数 - 适合光照条件较好的原始图像
    'sysu_visible_original_alpha': 2.5,    # 较高的alpha值，增强连接强度
    'sysu_visible_original_beta': 1.2,     # 适中的beta值，保持稳定性
    
    # 可见光增强图参数 - 适合经过数据增强的图像
    'sysu_visible_enhanced_alpha': 2.0,    # 略低的alpha值，适应增强后的变化
    'sysu_visible_enhanced_beta': 1.5,     # 较高的beta值，增加鲁棒性
    
    # 红外原图参数 - 适合热成像原始数据
    'sysu_thermal_original_alpha': 2.0,    # 适中的alpha值
    'sysu_thermal_original_beta': 1.3,     # 适中的beta值，保持热成像特性
    
    # 红外增强图参数 - 适合增强后的热成像数据
    'sysu_thermal_enhanced_alpha': 2.8,    # 较高的alpha值，增强热成像特征
    'sysu_thermal_enhanced_beta': 1.0,     # 较低的beta值，提高敏感性
}

# ============================================================================
# LLCM 数据集 Beta分布参数配置
# ============================================================================

LLCM_BETA_PARAMS = {
    # LLCM数据集模态差异较大，需要更强的对比度
    'llcm_visible_original_alpha': 3.0,    # 高alpha值，增强可见光特征
    'llcm_visible_original_beta': 1.0,     # 低beta值，提高敏感性
    
    'llcm_visible_enhanced_alpha': 2.2,    # 适中的alpha值
    'llcm_visible_enhanced_beta': 1.4,     # 适中的beta值
    
    'llcm_thermal_original_alpha': 2.5,    # 适中的alpha值
    'llcm_thermal_original_beta': 1.1,     # 略低的beta值
    
    'llcm_thermal_enhanced_alpha': 3.2,    # 高alpha值，增强热成像连接
    'llcm_thermal_enhanced_beta': 0.8,     # 低beta值，提高热成像敏感性
}

# ============================================================================
# RegDB 数据集 Beta分布参数配置
# ============================================================================

REGDB_BETA_PARAMS = {
    # RegDB数据集质量较稳定，参数相对保守
    'regdb_visible_original_alpha': 2.8,   # 较高的alpha值
    'regdb_visible_original_beta': 1.1,    # 略低的beta值
    
    'regdb_visible_enhanced_alpha': 2.3,   # 适中的alpha值
    'regdb_visible_enhanced_beta': 1.3,    # 适中的beta值
    
    'regdb_thermal_original_alpha': 2.6,   # 适中的alpha值
    'regdb_thermal_original_beta': 1.0,    # 低beta值
    
    'regdb_thermal_enhanced_alpha': 3.0,   # 较高的alpha值
    'regdb_thermal_enhanced_beta': 0.9,    # 较低的beta值
}

# ============================================================================
# 超图卷积相关参数配置
# ============================================================================

HYPERGRAPH_PARAMS = {
    'graphw': 1.0,              # 超图卷积权重，控制超图卷积的整体影响
    'theta1': 0.0,              # 稀疏化阈值，0.0表示不进行稀疏化
    'edge': 256,                # 超边数量，影响超图的复杂度
    'use_beta_distribution': True,  # 是否使用Beta分布
}

# ============================================================================
# LSE^HVD对角局部性参数配置
# ============================================================================

DIAGONAL_PARAMS = {
    'use_diagonal': True,           # 是否使用对角局部性优化
    'max_depth': 3,                 # 索引树最大深度
    'diagonal_threshold': 0.5,      # 对角相似度阈值
    'simplified_diagonal': False,   # 是否使用简化模式
    'fusion_strategy': 'advanced',  # 融合策略: simple_average, weighted_attention, advanced
}

# ============================================================================
# 参数调优建议
# ============================================================================

TUNING_SUGGESTIONS = {
    'alpha_base': {
        'range': (1.5, 4.0),
        'description': 'Alpha参数控制连接强度，值越大连接越强',
        'tuning_tips': [
            '对于特征丰富的模态（如可见光），可以使用较高的alpha值',
            '对于噪声较多的数据，适当降低alpha值',
            '增强图像通常需要比原图略低的alpha值'
        ]
    },
    'beta_base': {
        'range': (0.8, 2.0),
        'description': 'Beta参数控制连接的稳定性，值越大越保守',
        'tuning_tips': [
            '对于稳定的数据集，可以使用较高的beta值',
            '对于变化较大的数据，使用较低的beta值提高敏感性',
            '热成像数据通常需要较低的beta值'
        ]
    },
    'graphw': {
        'range': (0.5, 2.0),
        'description': '超图卷积整体权重，控制超图卷积的影响程度',
        'tuning_tips': [
            '初始训练时可以使用较小的值（0.5-1.0）',
            '如果超图卷积效果明显，可以逐渐增加到1.5-2.0',
            '过大的值可能导致训练不稳定'
        ]
    }
}

# ============================================================================
# 使用示例
# ============================================================================

def get_beta_params_for_dataset(dataset_name):
    """根据数据集名称获取对应的Beta分布参数"""
    if dataset_name.lower() == 'sysu':
        return SYSU_BETA_PARAMS
    elif dataset_name.lower() == 'llcm':
        return LLCM_BETA_PARAMS
    elif dataset_name.lower() == 'regdb':
        return REGDB_BETA_PARAMS
    else:
        print(f"未知数据集: {dataset_name}, 使用SYSU默认参数")
        return SYSU_BETA_PARAMS

def print_current_config(dataset_name):
    """打印当前数据集的配置"""
    params = get_beta_params_for_dataset(dataset_name)
    print(f"\n=== {dataset_name.upper()} 数据集 Beta分布参数配置 ===")
    for key, value in params.items():
        print(f"{key}: {value}")
    
    print(f"\n=== 超图卷积参数配置 ===")
    for key, value in HYPERGRAPH_PARAMS.items():
        print(f"{key}: {value}")
    
    print(f"\n=== 对角局部性参数配置 ===")
    for key, value in DIAGONAL_PARAMS.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    # 示例：打印所有数据集的配置
    for dataset in ['sysu', 'llcm', 'regdb']:
        print_current_config(dataset)
        print("\n" + "="*60 + "\n")
